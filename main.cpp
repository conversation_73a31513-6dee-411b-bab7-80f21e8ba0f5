#include "SpeechRecognizer.h"
#include <iostream>
#include <chrono>
#include <thread>

int main()
{
    SpeechRecognizer recognizer;

    std::cout << "Starting speech recognition for 10 seconds..." << std::endl;

    recognizer.startListening([](const std::string& text)
    {
        std::cout << "Recognized text: " << text << std::endl;
    });

    std::this_thread::sleep_for(std::chrono::seconds(10));

    recognizer.stopListening();

    std::cout << "Speech recognition stopped." << std::endl;

    return 0;
}
