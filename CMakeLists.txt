cmake_minimum_required(VERSION 3.31)
project(SpeechToText)

set(CMAKE_CONFIGURATION_TYPES "Debug;Release" CACHE STRING "" FORCE)
set(CMAKE_POSITION_INDEPENDENT_CODE ON)
set(CMAKE_BUILD_WITH_INSTALL_RPATH ON)


# Set RPATH to look in the loader directory first to load libraries.
if(APPLE)
    set(CMAKE_INSTALL_RPATH "@loader_path")
elseif (UNIX)
    set(CMAKE_INSTALL_RPATH "$ORIGIN")
endif()

# Set default build type as Release.
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -Wno-unused-function")   # Common parameters for debug and release.
set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g")
set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3")
set(CMAKE_OSX_DEPLOYMENT_TARGET "10.15" CACHE STRING "Minimum macOS deployment version")

set(TARGET_NAME speech-to-text-app)
add_executable(${TARGET_NAME} main.cpp SpeechRecognizer.mm)

set_target_properties(${TARGET_NAME} PROPERTIES
        MACOSX_BUNDLE_INFO_PLIST "${CMAKE_CURRENT_SOURCE_DIR}/Info.plist"
)

# Find and link the necessary frameworks
find_library(SPEECH_FRAMEWORK Speech)
find_library(AVFOUNDATION_FRAMEWORK AVFoundation)
find_library(FOUNDATION_FRAMEWORK Foundation)

target_link_libraries(${TARGET_NAME}
        PRIVATE
        ${SPEECH_FRAMEWORK}
        ${AVFOUNDATION_FRAMEWORK}
        ${FOUNDATION_FRAMEWORK}
)

# Set properties for Objective-C++ file
set_source_files_properties(SpeechRecognizer.mm PROPERTIES
        COMPILE_FLAGS "-x objective-c++"
)

install(TARGETS ${TARGET_NAME} RUNTIME DESTINATION .)
