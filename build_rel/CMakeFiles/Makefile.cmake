# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "/Users/<USER>/projects/stt/CMakeLists.txt"
  "CMakeFiles/3.31.3/CMakeCCompiler.cmake"
  "CMakeFiles/3.31.3/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.31.3/CMakeSystem.cmake"
  "/opt/local/share/cmake-3.31/Modules/CMakeCInformation.cmake"
  "/opt/local/share/cmake-3.31/Modules/CMakeCXXInformation.cmake"
  "/opt/local/share/cmake-3.31/Modules/CMakeCommonLanguageInclude.cmake"
  "/opt/local/share/cmake-3.31/Modules/CMakeGenericSystem.cmake"
  "/opt/local/share/cmake-3.31/Modules/CMakeInitializeConfigs.cmake"
  "/opt/local/share/cmake-3.31/Modules/CMakeLanguageInformation.cmake"
  "/opt/local/share/cmake-3.31/Modules/CMakeSystemSpecificInformation.cmake"
  "/opt/local/share/cmake-3.31/Modules/CMakeSystemSpecificInitialize.cmake"
  "/opt/local/share/cmake-3.31/Modules/Compiler/AppleClang-C.cmake"
  "/opt/local/share/cmake-3.31/Modules/Compiler/AppleClang-CXX.cmake"
  "/opt/local/share/cmake-3.31/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/opt/local/share/cmake-3.31/Modules/Compiler/Clang.cmake"
  "/opt/local/share/cmake-3.31/Modules/Compiler/GNU.cmake"
  "/opt/local/share/cmake-3.31/Modules/Internal/CMakeCLinkerInformation.cmake"
  "/opt/local/share/cmake-3.31/Modules/Internal/CMakeCXXLinkerInformation.cmake"
  "/opt/local/share/cmake-3.31/Modules/Internal/CMakeCommonLinkerInformation.cmake"
  "/opt/local/share/cmake-3.31/Modules/Linker/AppleClang-C.cmake"
  "/opt/local/share/cmake-3.31/Modules/Linker/AppleClang-CXX.cmake"
  "/opt/local/share/cmake-3.31/Modules/Linker/AppleClang.cmake"
  "/opt/local/share/cmake-3.31/Modules/Platform/Apple-AppleClang-C.cmake"
  "/opt/local/share/cmake-3.31/Modules/Platform/Apple-AppleClang-CXX.cmake"
  "/opt/local/share/cmake-3.31/Modules/Platform/Apple-Clang-C.cmake"
  "/opt/local/share/cmake-3.31/Modules/Platform/Apple-Clang-CXX.cmake"
  "/opt/local/share/cmake-3.31/Modules/Platform/Apple-Clang.cmake"
  "/opt/local/share/cmake-3.31/Modules/Platform/Darwin-Initialize.cmake"
  "/opt/local/share/cmake-3.31/Modules/Platform/Darwin.cmake"
  "/opt/local/share/cmake-3.31/Modules/Platform/Linker/Apple-AppleClang-C.cmake"
  "/opt/local/share/cmake-3.31/Modules/Platform/Linker/Apple-AppleClang-CXX.cmake"
  "/opt/local/share/cmake-3.31/Modules/Platform/Linker/Apple-AppleClang.cmake"
  "/opt/local/share/cmake-3.31/Modules/Platform/UnixPaths.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/speech-to-text-app.dir/DependInfo.cmake"
  )
