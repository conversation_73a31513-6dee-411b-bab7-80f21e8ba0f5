# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/local/bin/cmake

# The command to remove a file.
RM = /opt/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/projects/stt

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/projects/stt/build_rel

# Include any dependencies generated for this target.
include CMakeFiles/speech-to-text-app.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/speech-to-text-app.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/speech-to-text-app.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/speech-to-text-app.dir/flags.make

CMakeFiles/speech-to-text-app.dir/codegen:
.PHONY : CMakeFiles/speech-to-text-app.dir/codegen

CMakeFiles/speech-to-text-app.dir/main.cpp.o: CMakeFiles/speech-to-text-app.dir/flags.make
CMakeFiles/speech-to-text-app.dir/main.cpp.o: /Users/<USER>/projects/stt/main.cpp
CMakeFiles/speech-to-text-app.dir/main.cpp.o: CMakeFiles/speech-to-text-app.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/projects/stt/build_rel/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/speech-to-text-app.dir/main.cpp.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/speech-to-text-app.dir/main.cpp.o -MF CMakeFiles/speech-to-text-app.dir/main.cpp.o.d -o CMakeFiles/speech-to-text-app.dir/main.cpp.o -c /Users/<USER>/projects/stt/main.cpp

CMakeFiles/speech-to-text-app.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/speech-to-text-app.dir/main.cpp.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/projects/stt/main.cpp > CMakeFiles/speech-to-text-app.dir/main.cpp.i

CMakeFiles/speech-to-text-app.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/speech-to-text-app.dir/main.cpp.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/projects/stt/main.cpp -o CMakeFiles/speech-to-text-app.dir/main.cpp.s

CMakeFiles/speech-to-text-app.dir/SpeechRecognizer.mm.o: CMakeFiles/speech-to-text-app.dir/flags.make
CMakeFiles/speech-to-text-app.dir/SpeechRecognizer.mm.o: /Users/<USER>/projects/stt/SpeechRecognizer.mm
CMakeFiles/speech-to-text-app.dir/SpeechRecognizer.mm.o: CMakeFiles/speech-to-text-app.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/projects/stt/build_rel/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/speech-to-text-app.dir/SpeechRecognizer.mm.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -x objective-c++ -MD -MT CMakeFiles/speech-to-text-app.dir/SpeechRecognizer.mm.o -MF CMakeFiles/speech-to-text-app.dir/SpeechRecognizer.mm.o.d -o CMakeFiles/speech-to-text-app.dir/SpeechRecognizer.mm.o -c /Users/<USER>/projects/stt/SpeechRecognizer.mm

CMakeFiles/speech-to-text-app.dir/SpeechRecognizer.mm.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/speech-to-text-app.dir/SpeechRecognizer.mm.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -x objective-c++ -E /Users/<USER>/projects/stt/SpeechRecognizer.mm > CMakeFiles/speech-to-text-app.dir/SpeechRecognizer.mm.i

CMakeFiles/speech-to-text-app.dir/SpeechRecognizer.mm.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/speech-to-text-app.dir/SpeechRecognizer.mm.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -x objective-c++ -S /Users/<USER>/projects/stt/SpeechRecognizer.mm -o CMakeFiles/speech-to-text-app.dir/SpeechRecognizer.mm.s

# Object files for target speech-to-text-app
speech__to__text__app_OBJECTS = \
"CMakeFiles/speech-to-text-app.dir/main.cpp.o" \
"CMakeFiles/speech-to-text-app.dir/SpeechRecognizer.mm.o"

# External object files for target speech-to-text-app
speech__to__text__app_EXTERNAL_OBJECTS =

speech-to-text-app: CMakeFiles/speech-to-text-app.dir/main.cpp.o
speech-to-text-app: CMakeFiles/speech-to-text-app.dir/SpeechRecognizer.mm.o
speech-to-text-app: CMakeFiles/speech-to-text-app.dir/build.make
speech-to-text-app: CMakeFiles/speech-to-text-app.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/projects/stt/build_rel/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX executable speech-to-text-app"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/speech-to-text-app.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/speech-to-text-app.dir/build: speech-to-text-app
.PHONY : CMakeFiles/speech-to-text-app.dir/build

CMakeFiles/speech-to-text-app.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/speech-to-text-app.dir/cmake_clean.cmake
.PHONY : CMakeFiles/speech-to-text-app.dir/clean

CMakeFiles/speech-to-text-app.dir/depend:
	cd /Users/<USER>/projects/stt/build_rel && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/projects/stt /Users/<USER>/projects/stt /Users/<USER>/projects/stt/build_rel /Users/<USER>/projects/stt/build_rel /Users/<USER>/projects/stt/build_rel/CMakeFiles/speech-to-text-app.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/speech-to-text-app.dir/depend

