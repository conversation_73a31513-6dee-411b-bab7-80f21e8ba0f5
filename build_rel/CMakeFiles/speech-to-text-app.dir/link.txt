/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++  -Wall -Wextra -Wno-unused-function -O3 -DNDEBUG -O3 -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -Wl,-search_paths_first -Wl,-headerpad_max_install_names "CMakeFiles/speech-to-text-app.dir/main.cpp.o" "CMakeFiles/speech-to-text-app.dir/SpeechRecognizer.mm.o" -o speech-to-text-app  -Wl,-rpath,@loader_path -framework Speech -framework AVFoundation -framework Foundation
