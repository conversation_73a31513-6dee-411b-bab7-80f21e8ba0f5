# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/local/bin/cmake

# The command to remove a file.
RM = /opt/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/projects/stt

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/projects/stt/build_rel

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/speech-to-text-app.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/speech-to-text-app.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/speech-to-text-app.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/speech-to-text-app.dir

# All Build rule for target.
CMakeFiles/speech-to-text-app.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/speech-to-text-app.dir/build.make CMakeFiles/speech-to-text-app.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/speech-to-text-app.dir/build.make CMakeFiles/speech-to-text-app.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/projects/stt/build_rel/CMakeFiles --progress-num=1,2,3 "Built target speech-to-text-app"
.PHONY : CMakeFiles/speech-to-text-app.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/speech-to-text-app.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/projects/stt/build_rel/CMakeFiles 3
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/speech-to-text-app.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/projects/stt/build_rel/CMakeFiles 0
.PHONY : CMakeFiles/speech-to-text-app.dir/rule

# Convenience name for target.
speech-to-text-app: CMakeFiles/speech-to-text-app.dir/rule
.PHONY : speech-to-text-app

# codegen rule for target.
CMakeFiles/speech-to-text-app.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/speech-to-text-app.dir/build.make CMakeFiles/speech-to-text-app.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/projects/stt/build_rel/CMakeFiles --progress-num=1,2,3 "Finished codegen for target speech-to-text-app"
.PHONY : CMakeFiles/speech-to-text-app.dir/codegen

# clean rule for target.
CMakeFiles/speech-to-text-app.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/speech-to-text-app.dir/build.make CMakeFiles/speech-to-text-app.dir/clean
.PHONY : CMakeFiles/speech-to-text-app.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

